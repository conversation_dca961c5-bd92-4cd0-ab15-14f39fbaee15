// dnSpy decompiler from Assembly-CSharp.dll class: Assets.Scripts.Utils.NativeTools.impls.AndroidCaravanNativeTools
using Assets.Scripts.CasualTools.Common.Logging;
using Assets.Scripts.Logging;
using System;
using UnityEngine;

namespace Assets.Scripts.Utils.NativeTools.impls
{
    public class AndroidCaravanNativeTools : NativeToolsSupport
    {
        public AndroidCaravanNativeTools()
        {
            if (Application.platform == RuntimePlatform.WindowsEditor)
                return;

            //this._ajo = new AndroidJavaObject("caravan.peakgames.net.caravanandroidtools.CaravanTools", new object[0]);
            //this._privateFolder = this._ajo.GetStatic<string>("InternalStoragePath");
            //LogManager.Debug(LogTags.AndroidCaravanNativeTools, "Internal storage path is: {0}", new object[]
            //{
            //	this._privateFolder
            //});
            //this._callbacks = NativeToolsSupport.NativeProxy.AddComponent<AndroidCaravanNativeToolsCallbacks>();
        }

        public override string GetPrivateFolder()
        {
            return Application.persistentDataPath;
            return this._privateFolder;
        }

        public override void CreateLocalNotification(int id, int delay, string message, string title, bool useSound, bool useVibration)
        {
            //if (message == null)
            //{
            //	return;
            //}
            //byte[] bytes = Encoding.UTF8.GetBytes(message);
            //message = Convert.ToBase64String(bytes);
            //if (title == null)
            //{
            //	title = "Toon Blast";
            //}
            //this._ajo.CallStatic("createLocalNotification", new object[]
            //{
            //	delay,
            //	title,
            //	useSound,
            //	useVibration,
            //	message,
            //	id
            //});
        }

        public override void CancelNotifications()
        {
            //this._ajo.CallStatic("cancelNotifications", new object[0]);
        }

        public override void ShowNativeForceUpdateMessage(string title, string message, string button, string url)
        {
            //         Debug.Log("ShowNativeForceUpdateMessage:" + title + ", " + message + ", " + button + ", " + url);
            //         if (Application.platform == RuntimePlatform.WindowsEditor)
            //             return;

            //         AndroidJavaClass androidJavaClass = new AndroidJavaClass("com.unity3d.player.UnityPlayer");
            //AndroidJavaObject @static = androidJavaClass.GetStatic<AndroidJavaObject>("currentActivity");
            //@static.Call("runOnUiThread", new object[]
            //{
            //	new AndroidJavaRunnable(delegate()
            //	{
            //		this._ajo.CallStatic("showForceUpdatePopup", new object[]
            //		{
            //			message,
            //			button,
            //			url
            //		});
            //	})
            //});
        }

        public override void ShowConsentPopup(string title, string content, string accept, string terms, string privacy, string privacyUrl, string termsUrl)
        {
            //         Debug.Log("ShowConsentPopup:" + title + ", " + content + ", " + accept + ", " + terms);
            //         if (Application.platform == RuntimePlatform.WindowsEditor)
            //             return;

            //         AndroidJavaClass androidJavaClass = new AndroidJavaClass("com.unity3d.player.UnityPlayer");
            //AndroidJavaObject @static = androidJavaClass.GetStatic<AndroidJavaObject>("currentActivity");
            //@static.Call("runOnUiThread", new object[]
            //{
            //	new AndroidJavaRunnable(delegate()
            //	{
            //		this._ajo.CallStatic("showConsentPopup", new object[]
            //		{
            //			title,
            //			content,
            //			accept,
            //			terms,
            //			privacy,
            //			privacyUrl,
            //			termsUrl
            //		});
            //	})
            //});
        }

        public override void HideNativePopup()
        {
            //         Debug.Log("HideNativePopup");
            //         if (Application.platform == RuntimePlatform.WindowsEditor)
            //             return;

            //         AndroidJavaClass androidJavaClass = new AndroidJavaClass("com.unity3d.player.UnityPlayer");
            //AndroidJavaObject @static = androidJavaClass.GetStatic<AndroidJavaObject>("currentActivity");
            //@static.Call("runOnUiThread", new object[]
            //{
            //	new AndroidJavaRunnable(delegate()
            //	{
            //		this._ajo.CallStatic("hideNativePopup", new object[0]);
            //	})
            //});
        }

        public override void CancelNativeForceUpdateMessage()
        {
            //Debug.Log("CancelNativeForceUpdateMessage");
            //if (Application.platform == RuntimePlatform.WindowsEditor)
            //    return;

            //this._ajo.CallStatic("cancelForceUpdatePopup", new object[0]);
        }

        public override bool CanShowNativeReviewPopup()
        {
            return false;
            Debug.Log("CanShowNativeReviewPopup");
            if (Application.platform == RuntimePlatform.WindowsEditor)
                return false;

            LogManager.Debug(LogTags.AndroidCaravanNativeTools, "Native request review popup is not supported.", new object[0]);
            return false;
        }

        public override bool ShowNativeReviewPopup()
        {
            LogManager.Debug(LogTags.AndroidCaravanNativeTools, "Native request review popup is not supported.", new object[0]);
            return false;
        }

        public override bool IsBinaryDownloadedFromValidStore()
        {
            Debug.Log("IsBinaryDownloadedFromValidStore");
            return false;

            string text = this._ajo.CallStatic<string>("getSignatureText", new object[0]);
            LogManager.Debug(LogTags.AndroidCaravanNativeTools, "Native signature text is {0}", new object[]
            {
                text
            });
            return !string.IsNullOrEmpty(text) && text.Contains("62:37:75:65:24");
        }

        public override void StartTraceroute(string hostname)
        {
        }

        public override void ReadDataFromCloud(string key, Action<string> onRead)
        {
            Debug.Log("ReadDataFromCloud:" + key + " (DISABLED - Cloud storage functionality has been disabled)");
            // 云存储功能已被屏蔽，直接返回空字符串
            if (onRead != null)
            {
                onRead(string.Empty);
            }
            return;

            /*
            //         if (Application.platform == RuntimePlatform.WindowsEditor)
            //             return;

            //         this._callbacks.OnRead = onRead;
            //this._ajo.CallStatic("restoreData", new object[]
            //{
            //	key
            //});
            */
        }

        public override void WriteDataToCloud(string key, string value)
        {
            Debug.Log("WriteDataToCloud:" + key + " (DISABLED - Cloud storage functionality has been disabled)");
            // 云存储功能已被屏蔽，直接返回
            return;

            /*
            //         if (Application.platform == RuntimePlatform.WindowsEditor)
            //             return;

            //         this._ajo.CallStatic("backupData", new object[]
            //{
            //	key,
            //	value
            //});
            */
        }

        private readonly string _privateFolder;

        private AndroidJavaObject _ajo;

        private AndroidCaravanNativeToolsCallbacks _callbacks;
    }
}