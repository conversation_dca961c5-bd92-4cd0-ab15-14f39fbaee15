using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using Assets.Scripts.Backend.Commands;
using Assets.Scripts.DataHelpers;
using Assets.Scripts.DAO;
using System.IO;
using Assets.Scripts.CasualTools.Common.DB;

public class Tools
{
    [MenuItem("Assets/清档")]
    static void Clear()
    {
        PlayerPrefs.DeleteAll();
        Debug.LogError("dbPath; " + DatabaseManager.FullDbPath);
        if (File.Exists(DatabaseManager.FullDbPath))
        {
            File.Delete(DatabaseManager.FullDbPath);
        }
    }
}
